import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone
from PySide6.QtCore import QObject
import httpx

# Import the modules to test
from server import Server, extract_clean_html


class TestExtractCleanHtml:
    """Test the extract_clean_html utility function."""
    
    def test_extract_clean_html_simple(self):
        """Test extracting clean HTML from simple rich text."""
        rich_text = "<html><body><p>Hello World</p></body></html>"
        result = extract_clean_html(rich_text)
        assert result == "Hello World"
    
    def test_extract_clean_html_multiple_paragraphs(self):
        """Test extracting clean HTML from multiple paragraphs."""
        rich_text = "<html><body><p>First paragraph</p><p>Second paragraph</p></body></html>"
        result = extract_clean_html(rich_text)
        assert result == "First paragraph<br/>Second paragraph"
    
    def test_extract_clean_html_with_formatting(self):
        """Test extracting clean HTML preserving inner formatting."""
        rich_text = "<html><body><p>Text with <strong>bold</strong> and <em>italic</em></p></body></html>"
        result = extract_clean_html(rich_text)
        assert result == "Text with <strong>bold</strong> and <em>italic</em>"
    
    def test_extract_clean_html_empty_paragraphs(self):
        """Test extracting clean HTML skipping empty paragraphs."""
        rich_text = "<html><body><p>First</p><p></p><p>   </p><p>Last</p></body></html>"
        result = extract_clean_html(rich_text)
        assert result == "First<br/>Last"
    
    def test_extract_clean_html_no_body(self):
        """Test extracting clean HTML when no body tag exists."""
        rich_text = "<html><p>No body tag</p></html>"
        result = extract_clean_html(rich_text)
        assert result == ""
    
    def test_extract_clean_html_empty_input(self):
        """Test extracting clean HTML from empty input."""
        rich_text = ""
        result = extract_clean_html(rich_text)
        assert result == ""


class TestServer:
    """Test the Server class."""
    
    @pytest.fixture
    def server(self):
        """Create a Server instance for testing."""
        with patch('threading.Thread'):
            server = Server()
            # Mock the event loop to avoid actual threading
            server._loop = Mock()
            return server
    
    def test_server_initialization(self, server):
        """Test Server initialization."""
        assert isinstance(server, QObject)
        assert hasattr(server, 'experimentCount')
        assert hasattr(server, 'experimentFetched')
        assert hasattr(server, '_loop')
    
    def test_server_signals_exist(self, server):
        """Test that Server has the required signals."""
        # Check that signals exist and are callable
        assert callable(server.experimentCount.emit)
        assert callable(server.experimentFetched.emit)
    
    @pytest.mark.asyncio
    async def test_get_experiment_data_success(self, server):
        """Test successful experiment data retrieval."""
        # Mock response data
        mock_data = {
            "exp1": {"expNum": "123", "atomicMass": 50},
            "exp2": {"expNum": "124", "atomicMass": 60}
        }

        # Mock httpx client
        mock_response = Mock()
        mock_response.json.return_value = mock_data
        mock_response.raise_for_status.return_value = None

        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None

        with patch('httpx.AsyncClient', return_value=mock_client):
            await server._get_experiment_data("test_query")

            # Verify HTTP request was made with correct URL
            mock_client.get.assert_called_once_with("http://127.0.0.1:8080/experiments?test_query")

            # Verify response was processed
            mock_response.json.assert_called_once()
            mock_response.raise_for_status.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_experiment_data_http_error(self, server):
        """Test experiment data retrieval with HTTP error."""
        mock_client = AsyncMock()
        mock_client.get.side_effect = httpx.HTTPError("Connection failed")
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            with patch('builtins.print') as mock_print:
                await server._get_experiment_data("test_query")
                
                # Verify error was printed
                mock_print.assert_called_with("HTTP Error", mock_client.get.side_effect)
    
    @pytest.mark.asyncio
    async def test_get_experiment_data_status_error(self, server):
        """Test experiment data retrieval with HTTP status error."""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = httpx.HTTPStatusError("404 Not Found", request=Mock(), response=Mock())
        
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            with patch('builtins.print') as mock_print:
                await server._get_experiment_data("test_query")
                
                # Verify error was printed
                assert mock_print.called
    
    def test_get_experiment_data_slot(self, server):
        """Test the get_experiment_data slot method."""
        with patch('asyncio.run_coroutine_threadsafe') as mock_run:
            server.get_experiment_data("test_query")
            
            # Verify coroutine was scheduled
            mock_run.assert_called_once()
            args, _ = mock_run.call_args
            assert args[1] == server._loop  # Second argument should be the loop
    
    @pytest.mark.asyncio
    async def test_post_comment_success(self, server):
        """Test successful comment posting."""
        # Mock response
        mock_response = Mock()
        mock_response.text = "Success"
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            with patch('builtins.print') as mock_print:
                test_timestamp = 1234567890.0
                test_comment = "<html><body><p>Test comment</p></body></html>"
                
                await server.post_comment("EXP123", test_timestamp, test_comment)
                
                # Verify HTTP request was made
                mock_client.post.assert_called_once()
                call_args = mock_client.post.call_args
                
                # Check URL
                assert call_args[0][0] == "http://127.0.0.1:8080/comment"
                
                # Check headers
                headers = call_args[1]['headers']
                assert headers["Content-Type"] == "application/json"
                assert headers["Accept"] == "application/json"
                
                # Check JSON payload
                json_data = call_args[1]['json']
                assert json_data["expNum"] == "EXP123"
                assert json_data["comment"] == "Test comment"
                assert "timestamp" in json_data
                
                # Verify timestamp format (should be ISO format with Z)
                assert json_data["timestamp"].endswith("Z")
    
    @pytest.mark.asyncio
    async def test_post_comment_http_error(self, server):
        """Test comment posting with HTTP error."""
        mock_client = AsyncMock()
        mock_client.post.side_effect = httpx.HTTPError("Connection failed")
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            with patch('builtins.print') as mock_print:
                await server.post_comment("EXP123", 1234567890.0, "Test comment")
                
                # Verify error was printed
                mock_print.assert_called_with("HTTP Error", mock_client.post.side_effect)
    
    def test_timestamp_conversion(self):
        """Test timestamp conversion to Zulu format."""
        # Test with known timestamp
        test_timestamp = 1234567890.0  # 2009-02-13 23:31:30 UTC

        dt = datetime.fromtimestamp(test_timestamp, tz=timezone.utc)
        zulu_timestamp = dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

        # Should be in format: 2009-02-13T23:31:30.000Z
        assert zulu_timestamp == "2009-02-13T23:31:30.000Z"

    def test_html_cleaning_integration(self):
        """Test HTML cleaning integration in post_comment."""
        rich_text = "<html><body><p>Test <strong>bold</strong> text</p></body></html>"
        expected_clean = "Test <strong>bold</strong> text"

        result = extract_clean_html(rich_text)
        assert result == expected_clean


if __name__ == "__main__":
    pytest.main([__file__])
